#include "../include/types.h"
#include "../include/defs.h"
#include "../include/x86.h"
#include "../include/uart.h"
#include "../include/proc.h"
#include "../include/trap.h"
#include "../include/syscall.h"
#include "../include/mmu.h"

// 全局描述符表
struct segdesc gdt[6];

// 任务状态段
struct taskstate ts;

// 设置段描述符
static void
setsegdesc(struct segdesc *sd, uint base, uint lim, uint type)
{
  sd->lim_15_0 = lim;
  sd->base_15_0 = base;
  sd->base_23_16 = base >> 16;
  sd->type = type & 0xf;
  sd->s = (type >> 4) & 1;
  sd->dpl = (type >> 5) & 3;
  sd->p = (type >> 7) & 1;
  sd->lim_19_16 = lim >> 16;
  sd->avl = 0;
  sd->rsv1 = 0;
  sd->db = 1;
  sd->g = (lim >> 16) != 0;
  sd->base_31_24 = base >> 24;
}

// 初始化段描述符
void seginit(void)
{
  // 空描述符
  setsegdesc(&gdt[0], 0, 0, 0);

  // 内核代码段
  setsegdesc(&gdt[SEG_KCODE], 0, 0xffffffff, 0x9a);

  // 内核数据段
  setsegdesc(&gdt[SEG_KDATA], 0, 0xffffffff, 0x92);

  // 用户代码段
  setsegdesc(&gdt[SEG_UCODE], 0, 0xffffffff, 0xfa);

  // 用户数据段
  setsegdesc(&gdt[SEG_UDATA], 0, 0xffffffff, 0xf2);

  // TSS段
  setsegdesc(&gdt[SEG_TSS], (uint)&ts, sizeof(ts) - 1, 0x89);

  // 加载GDT
  setgdt(gdt, sizeof(gdt));

  // 初始化TSS
  ts.ss0 = SEG_KDATA << 3;
  ts.esp0 = 0; // 将在进程切换时设置

  // 加载TSS
  ltr(SEG_TSS << 3);

  cprintf("Segmentation initialized\n");
}

// 设置GDT
void setgdt(struct segdesc *gdt, int size)
{
  volatile struct
  {
    ushort limit;
    uint base;
  } __attribute__((packed)) gdtr;

  gdtr.limit = size - 1;
  gdtr.base = (uint)gdt;
  lgdt((void *)&gdtr);
}

// 内核主函数
void kmain(void)
{
  // 初始化控制台
  consoleinit();

  // 输出启动信息
  cprintf("MyOS - Mini Operating System with Batch Processing\n");
  cprintf("================================================\n");

  // 初始化串行端口
  uartinit();

  // 初始化段描述符和TSS
  seginit();

  // 初始化进程管理
  proc_init();

  // 初始化系统调用
  syscall_init();

  // 初始化中断处理
  trap_init();

  // 初始化批处理系统
  batch_init();

  // 启用中断
  sti();

  cprintf("\nKernel initialization completed!\n");
  cprintf("Starting batch processing system...\n");

  // 运行批处理系统
  batch_run();

  cprintf("\nAll programs completed. System halting...\n");

  // 系统完成，进入无限循环
  for (;;)
  {
    // 可以在这里添加简单的shell或其他交互功能
    uartpoll();
  }
}
