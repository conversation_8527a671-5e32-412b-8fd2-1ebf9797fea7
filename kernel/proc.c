#include "../include/types.h"
#include "../include/defs.h"
#include "../include/proc.h"
#include "../include/memlayout.h"
#include "../include/x86.h"
#include "../include/mmu.h"
#include "../include/trap.h"

// 外部变量
extern struct taskstate ts;

// 简单的memset实现
void memset(void *dst, int c, uint n)
{
    char *d = (char *)dst;
    while (n-- > 0)
        *d++ = c;
}

// 全局进程表
struct proc procs[MAXPROGS];
struct proc *current_proc = 0;
static uint next_pid = 1;

// 初始化进程管理
void proc_init(void)
{
    int i;
    for (i = 0; i < MAXPROGS; i++)
    {
        procs[i].state = UNUSED;
        procs[i].pid = 0;
    }
    cprintf("Process management initialized\n");
}

// 分配一个新进程
struct proc *proc_alloc(void)
{
    int i;
    char *sp;

    for (i = 0; i < MAXPROGS; i++)
    {
        if (procs[i].state == UNUSED)
        {
            procs[i].state = EMBRYO;
            procs[i].pid = next_pid++;
            procs[i].stack_base = USTACKBASE;
            procs[i].stack_size = USTACKSIZE;

            // 分配内核栈
            if ((procs[i].kstack = (char *)USERBASE + i * PROGSIZE - KSTACKSIZE) == 0)
                return 0;

            // 设置陷阱帧
            sp = procs[i].kstack + KSTACKSIZE;
            sp -= sizeof(*procs[i].tf);
            procs[i].tf = (struct trapframe *)sp;

            // 初始化上下文
            memset(&procs[i].context, 0, sizeof(procs[i].context));

            return &procs[i];
        }
    }
    return 0; // 没有可用的进程槽
}

// 释放进程
void proc_free(struct proc *p)
{
    if (p == 0)
        return;

    p->state = UNUSED;
    p->pid = 0;
    p->entry = 0;
    p->size = 0;
}

// 加载程序到进程（使用ELF加载器）
int proc_load(struct proc *p, void *binary, uint size)
{
    if (p == 0 || binary == 0 || size == 0)
        return -1;

    if (size > PROGSIZE)
        return -1; // 程序太大

    // 使用ELF加载器
    if (elf_load(p, binary, size) < 0)
    {
        cprintf("Failed to load ELF binary\n");
        return -1;
    }

    // 设置陷阱帧以便用户程序运行
    memset(p->tf, 0, sizeof(*p->tf));
    p->tf->cs = (SEG_UCODE << 3) | DPL_USER;
    p->tf->ds = (SEG_UDATA << 3) | DPL_USER;
    p->tf->es = (SEG_UDATA << 3) | DPL_USER;
    p->tf->fs = (SEG_UDATA << 3) | DPL_USER;
    p->tf->gs = (SEG_UDATA << 3) | DPL_USER;
    p->tf->ss = (SEG_UDATA << 3) | DPL_USER;
    p->tf->eflags = 0x202; // 启用中断
    p->tf->esp = p->stack_base + p->stack_size;
    p->tf->eip = p->entry;

    cprintf("Process %d loaded at 0x%x, size %d bytes\n", p->pid, p->entry, size);
    return 0;
}

// 运行进程（实现特权级切换）
void proc_run(struct proc *p)
{
    if (p == 0 || p->state != RUNNABLE)
        return;

    current_proc = p;
    p->state = RUNNING;

    cprintf("Running process %d (%s) at 0x%x\n", p->pid, p->name, p->entry);

    // 设置TSS的内核栈
    ts.esp0 = (uint)p->kstack + KSTACKSIZE;

    // 切换到用户态并运行进程
    // 这里使用iret指令从内核态切换到用户态
    asm volatile(
        "movl %0, %%esp\n\t" // 设置栈指针到陷阱帧
        "popl %%gs\n\t"      // 恢复段寄存器
        "popl %%fs\n\t"
        "popl %%es\n\t"
        "popl %%ds\n\t"
        "popal\n\t"          // 恢复通用寄存器
        "addl $8, %%esp\n\t" // 跳过trapno和err
        "iret\n\t"           // 返回用户态
        :
        : "r"(p->tf)
        : "memory");
}

// 进程退出
void proc_exit(int status)
{
    if (current_proc == 0)
        return;

    cprintf("Process %d (%s) exited with status %d\n",
            current_proc->pid, current_proc->name, status);

    current_proc->state = ZOMBIE;
    current_proc = 0;
}
