# 中断和异常处理汇编代码

.text

# 简化的中断向量表 - 只定义我们需要的
.globl vectors
vectors:
  .long vector0, vector1, vector2, vector3, vector4, vector5, vector6, vector7
  .long vector8, vector9, vector10, vector11, vector12, vector13, vector14, vector15
  .long vector16, vector17, vector18, vector19, vector20, vector21, vector22, vector23
  .long vector24, vector25, vector26, vector27, vector28, vector29, vector30, vector31
  .long vector32, vector33, vector34, vector35, vector36, vector37, vector38, vector39
  .long vector40, vector41, vector42, vector43, vector44, vector45, vector46, vector47
  .long vector48, vector49, vector50, vector51, vector52, vector53, vector54, vector55
  .long vector56, vector57, vector58, vector59, vector60, vector61, vector62, vector63
  .long vector64, vector65, vector66, vector67, vector68, vector69, vector70, vector71
  .long vector72, vector73, vector74, vector75, vector76, vector77, vector78, vector79
  .long vector80, vector81, vector82, vector83, vector84, vector85, vector86, vector87
  .long vector88, vector89, vector90, vector91, vector92, vector93, vector94, vector95
  .long vector96, vector97, vector98, vector99, vector100, vector101, vector102, vector103
  .long vector104, vector105, vector106, vector107, vector108, vector109, vector110, vector111
  .long vector112, vector113, vector114, vector115, vector116, vector117, vector118, vector119
  .long vector120, vector121, vector122, vector123, vector124, vector125, vector126, vector127
  .long vector128, vector129, vector130, vector131, vector132, vector133, vector134, vector135
  .long vector136, vector137, vector138, vector139, vector140, vector141, vector142, vector143
  .long vector144, vector145, vector146, vector147, vector148, vector149, vector150, vector151
  .long vector152, vector153, vector154, vector155, vector156, vector157, vector158, vector159
  .long vector160, vector161, vector162, vector163, vector164, vector165, vector166, vector167
  .long vector168, vector169, vector170, vector171, vector172, vector173, vector174, vector175
  .long vector176, vector177, vector178, vector179, vector180, vector181, vector182, vector183
  .long vector184, vector185, vector186, vector187, vector188, vector189, vector190, vector191
  .long vector192, vector193, vector194, vector195, vector196, vector197, vector198, vector199
  .long vector200, vector201, vector202, vector203, vector204, vector205, vector206, vector207
  .long vector208, vector209, vector210, vector211, vector212, vector213, vector214, vector215
  .long vector216, vector217, vector218, vector219, vector220, vector221, vector222, vector223
  .long vector224, vector225, vector226, vector227, vector228, vector229, vector230, vector231
  .long vector232, vector233, vector234, vector235, vector236, vector237, vector238, vector239
  .long vector240, vector241, vector242, vector243, vector244, vector245, vector246, vector247
  .long vector248, vector249, vector250, vector251, vector252, vector253, vector254, vector255

# 宏：生成不带错误码的中断处理程序
.macro TRAPHANDLER_NOEC num
vector\num:
  pushl $0
  pushl $\num
  jmp alltraps
.endm

# 宏：生成带错误码的中断处理程序
.macro TRAPHANDLER num
vector\num:
  pushl $\num
  jmp alltraps
.endm

# 生成所有中断向量
TRAPHANDLER_NOEC 0
TRAPHANDLER_NOEC 1
TRAPHANDLER_NOEC 2
TRAPHANDLER_NOEC 3
TRAPHANDLER_NOEC 4
TRAPHANDLER_NOEC 5
TRAPHANDLER_NOEC 6
TRAPHANDLER_NOEC 7
TRAPHANDLER 8
TRAPHANDLER_NOEC 9
TRAPHANDLER 10
TRAPHANDLER 11
TRAPHANDLER 12
TRAPHANDLER 13
TRAPHANDLER 14
TRAPHANDLER_NOEC 15
TRAPHANDLER_NOEC 16
TRAPHANDLER 17
TRAPHANDLER_NOEC 18
TRAPHANDLER_NOEC 19
TRAPHANDLER_NOEC 20
TRAPHANDLER_NOEC 21
TRAPHANDLER_NOEC 22
TRAPHANDLER_NOEC 23
TRAPHANDLER_NOEC 24
TRAPHANDLER_NOEC 25
TRAPHANDLER_NOEC 26
TRAPHANDLER_NOEC 27
TRAPHANDLER_NOEC 28
TRAPHANDLER_NOEC 29
TRAPHANDLER_NOEC 30
TRAPHANDLER_NOEC 31

# 生成其余中断向量 - 只生成我们需要的
TRAPHANDLER_NOEC 32
TRAPHANDLER_NOEC 33
TRAPHANDLER_NOEC 34
TRAPHANDLER_NOEC 35
TRAPHANDLER_NOEC 36
TRAPHANDLER_NOEC 37
TRAPHANDLER_NOEC 38
TRAPHANDLER_NOEC 39
TRAPHANDLER_NOEC 40
TRAPHANDLER_NOEC 41
TRAPHANDLER_NOEC 42
TRAPHANDLER_NOEC 43
TRAPHANDLER_NOEC 44
TRAPHANDLER_NOEC 45
TRAPHANDLER_NOEC 46
TRAPHANDLER_NOEC 47
TRAPHANDLER_NOEC 48
TRAPHANDLER_NOEC 49
TRAPHANDLER_NOEC 50
TRAPHANDLER_NOEC 51
TRAPHANDLER_NOEC 52
TRAPHANDLER_NOEC 53
TRAPHANDLER_NOEC 54
TRAPHANDLER_NOEC 55
TRAPHANDLER_NOEC 56
TRAPHANDLER_NOEC 57
TRAPHANDLER_NOEC 58
TRAPHANDLER_NOEC 59
TRAPHANDLER_NOEC 60
TRAPHANDLER_NOEC 61
TRAPHANDLER_NOEC 62
TRAPHANDLER_NOEC 63
TRAPHANDLER_NOEC 64
TRAPHANDLER_NOEC 65
TRAPHANDLER_NOEC 66
TRAPHANDLER_NOEC 67
TRAPHANDLER_NOEC 68
TRAPHANDLER_NOEC 69
TRAPHANDLER_NOEC 70
TRAPHANDLER_NOEC 71
TRAPHANDLER_NOEC 72
TRAPHANDLER_NOEC 73
TRAPHANDLER_NOEC 74
TRAPHANDLER_NOEC 75
TRAPHANDLER_NOEC 76
TRAPHANDLER_NOEC 77
TRAPHANDLER_NOEC 78
TRAPHANDLER_NOEC 79
TRAPHANDLER_NOEC 80
TRAPHANDLER_NOEC 81
TRAPHANDLER_NOEC 82
TRAPHANDLER_NOEC 83
TRAPHANDLER_NOEC 84
TRAPHANDLER_NOEC 85
TRAPHANDLER_NOEC 86
TRAPHANDLER_NOEC 87
TRAPHANDLER_NOEC 88
TRAPHANDLER_NOEC 89
TRAPHANDLER_NOEC 90
TRAPHANDLER_NOEC 91
TRAPHANDLER_NOEC 92
TRAPHANDLER_NOEC 93
TRAPHANDLER_NOEC 94
TRAPHANDLER_NOEC 95
TRAPHANDLER_NOEC 96
TRAPHANDLER_NOEC 97
TRAPHANDLER_NOEC 98
TRAPHANDLER_NOEC 99
TRAPHANDLER_NOEC 100
TRAPHANDLER_NOEC 101
TRAPHANDLER_NOEC 102
TRAPHANDLER_NOEC 103
TRAPHANDLER_NOEC 104
TRAPHANDLER_NOEC 105
TRAPHANDLER_NOEC 106
TRAPHANDLER_NOEC 107
TRAPHANDLER_NOEC 108
TRAPHANDLER_NOEC 109
TRAPHANDLER_NOEC 110
TRAPHANDLER_NOEC 111
TRAPHANDLER_NOEC 112
TRAPHANDLER_NOEC 113
TRAPHANDLER_NOEC 114
TRAPHANDLER_NOEC 115
TRAPHANDLER_NOEC 116
TRAPHANDLER_NOEC 117
TRAPHANDLER_NOEC 118
TRAPHANDLER_NOEC 119
TRAPHANDLER_NOEC 120
TRAPHANDLER_NOEC 121
TRAPHANDLER_NOEC 122
TRAPHANDLER_NOEC 123
TRAPHANDLER_NOEC 124
TRAPHANDLER_NOEC 125
TRAPHANDLER_NOEC 126
TRAPHANDLER_NOEC 127
TRAPHANDLER_NOEC 128
TRAPHANDLER_NOEC 129
TRAPHANDLER_NOEC 130
TRAPHANDLER_NOEC 131
TRAPHANDLER_NOEC 132
TRAPHANDLER_NOEC 133
TRAPHANDLER_NOEC 134
TRAPHANDLER_NOEC 135
TRAPHANDLER_NOEC 136
TRAPHANDLER_NOEC 137
TRAPHANDLER_NOEC 138
TRAPHANDLER_NOEC 139
TRAPHANDLER_NOEC 140
TRAPHANDLER_NOEC 141
TRAPHANDLER_NOEC 142
TRAPHANDLER_NOEC 143
TRAPHANDLER_NOEC 144
TRAPHANDLER_NOEC 145
TRAPHANDLER_NOEC 146
TRAPHANDLER_NOEC 147
TRAPHANDLER_NOEC 148
TRAPHANDLER_NOEC 149
TRAPHANDLER_NOEC 150
TRAPHANDLER_NOEC 151
TRAPHANDLER_NOEC 152
TRAPHANDLER_NOEC 153
TRAPHANDLER_NOEC 154
TRAPHANDLER_NOEC 155
TRAPHANDLER_NOEC 156
TRAPHANDLER_NOEC 157
TRAPHANDLER_NOEC 158
TRAPHANDLER_NOEC 159
TRAPHANDLER_NOEC 160
TRAPHANDLER_NOEC 161
TRAPHANDLER_NOEC 162
TRAPHANDLER_NOEC 163
TRAPHANDLER_NOEC 164
TRAPHANDLER_NOEC 165
TRAPHANDLER_NOEC 166
TRAPHANDLER_NOEC 167
TRAPHANDLER_NOEC 168
TRAPHANDLER_NOEC 169
TRAPHANDLER_NOEC 170
TRAPHANDLER_NOEC 171
TRAPHANDLER_NOEC 172
TRAPHANDLER_NOEC 173
TRAPHANDLER_NOEC 174
TRAPHANDLER_NOEC 175
TRAPHANDLER_NOEC 176
TRAPHANDLER_NOEC 177
TRAPHANDLER_NOEC 178
TRAPHANDLER_NOEC 179
TRAPHANDLER_NOEC 180
TRAPHANDLER_NOEC 181
TRAPHANDLER_NOEC 182
TRAPHANDLER_NOEC 183
TRAPHANDLER_NOEC 184
TRAPHANDLER_NOEC 185
TRAPHANDLER_NOEC 186
TRAPHANDLER_NOEC 187
TRAPHANDLER_NOEC 188
TRAPHANDLER_NOEC 189
TRAPHANDLER_NOEC 190
TRAPHANDLER_NOEC 191
TRAPHANDLER_NOEC 192
TRAPHANDLER_NOEC 193
TRAPHANDLER_NOEC 194
TRAPHANDLER_NOEC 195
TRAPHANDLER_NOEC 196
TRAPHANDLER_NOEC 197
TRAPHANDLER_NOEC 198
TRAPHANDLER_NOEC 199
TRAPHANDLER_NOEC 200
TRAPHANDLER_NOEC 201
TRAPHANDLER_NOEC 202
TRAPHANDLER_NOEC 203
TRAPHANDLER_NOEC 204
TRAPHANDLER_NOEC 205
TRAPHANDLER_NOEC 206
TRAPHANDLER_NOEC 207
TRAPHANDLER_NOEC 208
TRAPHANDLER_NOEC 209
TRAPHANDLER_NOEC 210
TRAPHANDLER_NOEC 211
TRAPHANDLER_NOEC 212
TRAPHANDLER_NOEC 213
TRAPHANDLER_NOEC 214
TRAPHANDLER_NOEC 215
TRAPHANDLER_NOEC 216
TRAPHANDLER_NOEC 217
TRAPHANDLER_NOEC 218
TRAPHANDLER_NOEC 219
TRAPHANDLER_NOEC 220
TRAPHANDLER_NOEC 221
TRAPHANDLER_NOEC 222
TRAPHANDLER_NOEC 223
TRAPHANDLER_NOEC 224
TRAPHANDLER_NOEC 225
TRAPHANDLER_NOEC 226
TRAPHANDLER_NOEC 227
TRAPHANDLER_NOEC 228
TRAPHANDLER_NOEC 229
TRAPHANDLER_NOEC 230
TRAPHANDLER_NOEC 231
TRAPHANDLER_NOEC 232
TRAPHANDLER_NOEC 233
TRAPHANDLER_NOEC 234
TRAPHANDLER_NOEC 235
TRAPHANDLER_NOEC 236
TRAPHANDLER_NOEC 237
TRAPHANDLER_NOEC 238
TRAPHANDLER_NOEC 239
TRAPHANDLER_NOEC 240
TRAPHANDLER_NOEC 241
TRAPHANDLER_NOEC 242
TRAPHANDLER_NOEC 243
TRAPHANDLER_NOEC 244
TRAPHANDLER_NOEC 245
TRAPHANDLER_NOEC 246
TRAPHANDLER_NOEC 247
TRAPHANDLER_NOEC 248
TRAPHANDLER_NOEC 249
TRAPHANDLER_NOEC 250
TRAPHANDLER_NOEC 251
TRAPHANDLER_NOEC 252
TRAPHANDLER_NOEC 253
TRAPHANDLER_NOEC 254
TRAPHANDLER_NOEC 255

# 通用中断处理程序
.globl alltraps
alltraps:
  # 保存段寄存器（按trapframe结构的顺序）
  pushl %gs
  pushl %fs
  pushl %es
  pushl %ds

  # 保存通用寄存器
  pushal

  # 设置内核数据段
  movw $0x10, %ax   # SEG_KDATA << 3
  movw %ax, %ds
  movw %ax, %es

  # 调用C语言中断处理程序
  pushl %esp  # 传递trapframe指针
  call trap_handler
  addl $4, %esp

# 全局符号，用于从中断返回
.globl trapret
trapret:
  # 恢复通用寄存器
  popal

  # 恢复段寄存器
  popl %gs
  popl %fs
  popl %es
  popl %ds

  # 清理中断号和错误码
  addl $8, %esp

  # 返回
  iret
